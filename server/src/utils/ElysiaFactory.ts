import { getDB, initializeConnection } from "@config/mongo";
import { authMiddleware } from "@middleware/betterAuth";
import { Elysia } from "elysia";
import { cors } from "@elysiajs/cors";
import swagger from "@elysiajs/swagger";

export type ElysiaAuthInstance = ReturnType<typeof authMiddleware>;

class ElysiaFactory {
    private _elysiaServer: ElysiaAuthInstance | null;
    private _authMiddleware: ElysiaAuthInstance | null;

    constructor() {
        this._elysiaServer = null;
        this._authMiddleware = null;
    }



    get elysia() {
        return this._elysiaServer;
    }

    createElysia(config?: ConstructorParameters<typeof Elysia>[0]) {
        if (!this._authMiddleware) {
            throw new Error("Elysia auth middleware not initialized");
        }
        return (new Elysia({ ...config })) as unknown as ElysiaAuthInstance;
    }

    async initializeElysia(): Promise<ElysiaAuthInstance> {
        await initializeConnection();

        // Optional: Get the native DB instance if needed
        const db = getDB();

        // Initialize auth with the established connection
        const authMiddle = authMiddleware(db);
        this._authMiddleware = authMiddle;

        this._elysiaServer = new Elysia<string>()
            .use(
                cors({
                    origin: "*",
                    methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
                    credentials: true,
                    allowedHeaders: ["Content-Type", "Authorization"],
                }),
            )
            .use(swagger())
            .use(authMiddle)


        return this._elysiaServer;
    }
}

const elysiaInstance = new ElysiaFactory();

export const initializeElysia = async () => {
    return elysiaInstance.initializeElysia();
}

export const getElysia = () => {
    return elysiaInstance.elysia;
}

export const createElysia = (config?: ConstructorParameters<typeof Elysia>[0]) => {
    return elysiaInstance.createElysia(config);
}  
